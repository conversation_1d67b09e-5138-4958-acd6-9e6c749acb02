package dataQuery

import (
	"deskcrm/api/examcore"
	"deskcrm/conf"

	"github.com/gin-gonic/gin"
)

// DataCenter 数据中心服务，对应PHP中的AssistantDesk_Data_Duxuesc_DataCenter
type DataCenter struct {
	examcoreClient *examcore.Client
}

// NewDataCenter 创建数据中心实例
func NewDataCenter() *DataCenter {
	return &DataCenter{
		examcoreClient: examcore.NewClient(),
	}
}

// GetBottomTestSubjectList 获取摸底测的科目列表
// 对应PHP中的AssistantDesk_Data_Duxuesc_DataCenter::getBottomTestSubjectList()
func (dc *DataCenter) GetBottomTestSubjectList(ctx *gin.Context, cpuId int64) (map[int]int, error) {
	if cpuId == 0 {
		return make(map[int]int), nil
	}

	// 调用examcore客户端获取摸底测学科列表
	return dc.examcoreClient.GetUrgeBottomTestSubjectList(ctx, cpuId)
}

// GetCourseInfo 获取课程信息
// 这里需要根据实际的课程服务来实现
func (dc *DataCenter) GetCourseInfo(ctx *gin.Context, courseId int64) (map[string]interface{}, error) {
	// TODO: 这里需要调用实际的课程信息服务
	// 临时返回空数据，实际使用时需要连接到相应的课程服务
	return make(map[string]interface{}), nil
}