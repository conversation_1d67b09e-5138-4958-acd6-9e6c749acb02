# KeyBehavior initData 方法详细分析

## 概述
`Service_Page_DeskV1_Student_KeyBehavior::initData()` 方法包含10个子方法，用于收集学生的关键行为数据。本文档详细梳理每个子方法的功能、数据源和字段使用情况。

## 方法执行顺序及详细分析

### 1. initCourseLessonInfos() - 课程章节信息
**位置：** 第221-230行  
**功能：** 获取课程基础信息和章节列表  
**数据源：** `Api_Dal::getCourseLessonInfoByCourseIds($this->courseId)`  
**输入字段：** courseId  
**输出属性：** 
- `lessonInfos`（章节列表）
- `courseInfo`（课程信息，包含mainGradeId等）
- `lessonCnt`（章节总数）

**异常处理：** 当获取课程信息失败时抛出异常

### 2. initLpcLUData() - LPC学习记录
**位置：** 第232-247行  
**功能：** 获取学生LPC学习数据  
**数据源：** `Assistant_Common_Service_DataService_Query_LessonStudentData::getLpcListByCourseStudent()`  
**输入字段：** courseId, studentUid  
**查询字段：** ['lpc_uid', 'course_id', 'student_uid', 'lesson_id', 'leads_id', 'playback_time']  
**输出属性：** `lpcLUData`（按lesson_id索引的数组）

**容错处理：** 查询失败时返回空数组并记录警告日志

### 3. initPreCompliTestData() - 摸底测试
**位置：** 第127-147行  
**功能：** 检查摸底测试完成状态  
**数据源准备：** 向DataSource添加"placement_test_submit_detail"字段  
**数据源：** `AssistantDesk_Data_Duxuesc_Format::formatPreCompliTestData($this->leadsId, $this->studentUid)`  
**输入参数：** leadsId, studentUid  
**关键字段：** placementTestScoreFirst, placementTestScoreSecond  
**逻辑判断：** 检查所有测试分数是否都不为-1（完成状态）  
**输出属性：** `preCompliTestData`（boolean）

### 4. initNeedSurveyStatus() - 挖需问卷
**位置：** 第150-156行  
**功能：** 获取挖需问卷填写状态  
**数据源：** `AssistantDesk_Data_Duxuesc_Format::formatNeedSurveyStatus($this->leadsId, $this->studentUid)`  
**输入参数：** leadsId, studentUid  
**逻辑判断：** status == 1 时表示已完成  
**输出属性：** `needSurveyStatus`（boolean）

### 5. initOrderStatus() - 工作台预约问卷  
**位置：** 第159-164行  
**功能：** 获取工作台预约问卷状态  
**数据源：** `AssistantDesk_Data_Duxuesc_Format::formatOrderStatus($this->leadsId, $this->studentUid)`  
**输入参数：** leadsId, studentUid  
**输出属性：** `orderStatus`（boolean）

### 6. initFnOrderStatus() - 蜂鸟预约问卷
**位置：** 第167-172行  
**功能：** 获取蜂鸟预约问卷状态  
**数据源：** `AssistantDesk_Data_Duxuesc_Format::formatFnOrderStatus($this->leadsId, $this->studentUid)`  
**输入参数：** leadsId, studentUid  
**输出属性：** `fnOrderStatus`（boolean）

### 7. initAttendNum() - 到课统计
**位置：** 第175-178行  
**功能：** 获取学生到课次数  
**数据源：** `AssistantDesk_Data_Duxuesc_Format::formatAttendNum($this->leadsId, $this->studentUid)`  
**输入参数：** leadsId, studentUid  
**输出属性：** `attendNum`（数字）

**日志记录：** 记录到课数量到日志

### 8. initFinishNum() - 完课统计
**位置：** 第181-184行  
**功能：** 获取学生完课次数  
**数据源：** `AssistantDesk_Data_Duxuesc_Format::formatFinishNum($this->leadsId, $this->studentUid)`  
**输入参数：** leadsId, studentUid  
**输出属性：** `finishNum`（数字）

### 9. initLpcPlayStatus() - 回放观看统计
**位置：** 第192-207行  
**功能：** 计算有效回放观看次数  
**依赖数据：** 
- `lessonInfos`（来自initCourseLessonInfos方法）
- `lpcLUData`（来自initLpcLUData方法）

**处理逻辑：**
1. 遍历所有章节信息
2. 排除未开始的课程（startTime > time()）
3. 统计观看时长≥300秒（5分钟）的课程数量

**关键字段：** startTime, playback_time  
**输出属性：** `playStatusCnt`（有效回放次数）

### 10. initPreOrderInfos() - 预支付状态
**位置：** 第209-219行  
**功能：** 检查定金尾款支付状态  
**数据源：** `(new Service_Data_PreOrderList())->getAllStuPreOrderInfos([$this->studentUid])`  
**输入参数：** [studentUid]（数组格式）  
**处理逻辑：** 
1. 获取学生预订单信息列表
2. 按studentUid建立索引
3. 检查status == 3（表示定金尾款已支付）

**关键字段：** studentUid, status  
**输出属性：** `preOrderStatus`（0或1）

## 数据源汇总

| 序号 | 数据源 | 用途 | 调用方法数量 |
|------|--------|------|------------|
| 1 | `Api_Dal` | 课程基础信息获取 | 1 |
| 2 | `Assistant_Common_Service_DataService_Query_LessonStudentData` | LPC学习记录查询 | 1 |
| 3 | `AssistantDesk_Data_Duxuesc_Format` | 学生行为数据格式化 | 6 |
| 4 | `Service_Data_PreOrderList` | 预订单服务 | 1 |
| 5 | `AssistantDesk_Data_DataSource` | 数据源字段管理 | 1 |

## 核心字段依赖

### 输入参数
- `studentUid` - 学生用户ID
- `courseId` - 课程ID  
- `leadsId` - 线索ID

### DataSource 添加字段
- "attend_num" - 到课数量
- "lbp_attend_num" - LBP到课数量
- "finish_num" - 完课数量
- "lbp_finish_num" - LBP完课数量
- "placement_test_submit_detail" - 摸底测试提交详情

### 关键业务字段
- `placementTestScoreFirst/Second` - 摸底测试分数
- `startTime` - 课程开始时间
- `playback_time` - 回放观看时长
- `status` - 各种状态字段

## 方法间依赖关系

```
initCourseLessonInfos() → lessonInfos, courseInfo
        ↓
initLpcLUData() → lpcLUData
        ↓
initLpcPlayStatus() (依赖 lessonInfos + lpcLUData)
```

其他7个方法相对独立，各自从不同数据源获取状态信息。

## 输出数据用途

所有初始化的私有属性最终用于 `formatData()` 方法，根据课程年级（mainGradeId）不同，展示不同的学生关键行为指标组合：

- **小学（1, 11-16）：** 摸底测、挖需问卷、工作台预约问卷、蜂鸟预约问卷、到课、完课
- **初中（2-4, 20）：** 蜂鸟预约问卷、到课、完课、回放
- **高中（5-7, 30）：** 到课、完课、回放、预支付

## 总结

`initData` 方法采用典型的数据聚合模式，从多个数据源收集学生的关键行为数据，为后续的分组展示和业务分析提供完整的数据基础。每个子方法职责单一，便于维护和扩展。