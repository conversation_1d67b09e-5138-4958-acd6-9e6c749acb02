{"permissions": {"allow": ["mcp__feedback-mcp__interactive_feedback", "mcp__sequential-thinking__sequentialthinking", "Bash(sed -i '' '1067,$d' /Users/<USER>/Documents/interface/assistantdesk/migration/performancev1/迁移文档.md)", "Bash(find:*)", "Bash(rm:*)", "Bash(brew tap:*)", "Bash(brew install:*)", "Bash(brew list:*)", "Bash(brew search:*)", "Bash(ls:*)", "Bash(go build:*)", "Bash(grep:*)", "Bash(go run test_detail_config.go)", "Bash(mkdir -p /Users/<USER>/Documents/interface/deskcrm/service/enterprise)", "Bash(go vet ./service/enterprise/... ./models/tblAssistantWxType.go ./models/tblManualBindData.go ./service/ui/studentService.go)", "Bash(go vet ./service/enterprise/ ./models/ ./service/ui/)", "Bash(go vet ./service/enterprise/ ./models/tblAssistantWxType.go ./models/tblManualBindData.go ./models/tblShowWxCourse.go ./consts/wechat.go)", "Bash(go vet ./service/enterprise/enterpriseWxService.go ./models/tblAssistantWxType.go ./models/tblManualBindData.go ./consts/wechat.go)", "<PERSON><PERSON>(mv:*)"], "deny": []}}